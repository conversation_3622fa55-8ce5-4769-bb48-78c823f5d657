.BuyerRequests {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following consistent dashboard pattern */
.BuyerRequests .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
}

.BuyerRequests .table th,
.BuyerRequests .table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.BuyerRequests .table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.BuyerRequests .table tbody tr {
  transition: background-color 0.2s ease;
}

.BuyerRequests .table tbody tr:hover {
  background-color: var(--primary-light-color);
}

.BuyerRequests .table tbody tr:last-child td {
  border-bottom: none;
}

.BuyerRequests .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerRequests .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerRequests .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerRequests .content-info {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.BuyerRequests .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerRequests .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerRequests .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerRequests .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerRequests .status-badge.approved {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerRequests .status-badge.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

/* Action buttons styling - Following consistent dashboard pattern */
.BuyerRequests .action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.BuyerRequests .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: var(--text-color);
}

.BuyerRequests .action-btn:hover {
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  transform: scale(1.05);
}

.BuyerRequests__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.BuyerRequests__empty h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.BuyerRequests__empty p {
  font-size: var(--basefont);
  color: var(--dark-gray);
}

/* Enhanced Requests Styles - Following consistent dashboard pattern */
.BuyerRequests .requests-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--heading5);
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.BuyerRequests .filter-group,
.BuyerRequests .search-group {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .filter-select,
.BuyerRequests .search-input {
  padding: 10px var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background: var(--white);
  transition: all 0.3s ease;
}

.BuyerRequests .filter-select:focus,
.BuyerRequests .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(236, 29, 59, 0.1);
}

.BuyerRequests .search-input {
  min-width: 200px;
}

.BuyerRequests .requests-stats {
  display: flex;
  gap: var(--heading5);
  margin-bottom: var(--heading5);
}

.BuyerRequests .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius);
  min-width: 100px;
  box-shadow: var(--box-shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.BuyerRequests .stat-item:hover {
  transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

.BuyerRequests .stat-count {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.BuyerRequests .stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  text-align: center;
}

.BuyerRequests .request-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.BuyerRequests .request-meta {
  display: flex;
  gap: var(--border-radius-medium);
}

.BuyerRequests .content-type,
.BuyerRequests .sport {
  font-size: var(--smallfont);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  color: var(--dark-gray);
}

.BuyerRequests .request-description {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  line-height: 1.4;
}

.BuyerRequests .seller-info {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .seller-name {
  font-weight: 500;
  color: var(--secondary-color);
}

.BuyerRequests .seller-email {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.BuyerRequests .budget-info {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .budget-amount {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--heading6);
}

.BuyerRequests .seller-price {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Status badge variations - Using consistent color scheme */
.BuyerRequests .status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerRequests .status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerRequests .status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.BuyerRequests .status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerRequests .status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.BuyerRequests .status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Specific action button styles - Following dashboard pattern */
.BuyerRequests .view-btn {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerRequests .view-btn:hover {
  background: #3498db;
  color: var(--white);
  transform: scale(1.05);
}

.BuyerRequests .payment-btn {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerRequests .payment-btn:hover {
  background: #f39c12;
  color: var(--white);
  transform: scale(1.05);
}

.BuyerRequests .empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

/* Responsive styles - Following consistent dashboard pattern */
@media (max-width: 1024px) {
  .BuyerRequests .requests-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .BuyerRequests .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerRequests .table {
    font-size: var(--extrasmallfont);
  }

  .BuyerRequests .table th,
  .BuyerRequests .table td {
    padding: 8px 6px;
  }

  .BuyerRequests .requests-stats {
    flex-wrap: wrap;
    gap: var(--smallfont);
  }

  .BuyerRequests .stat-item {
    min-width: 80px;
    padding: var(--smallfont);
  }

  .BuyerRequests .search-input {
    min-width: auto;
  }

  .BuyerRequests .action-buttons {
    gap: 2px;
  }

  .BuyerRequests .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }

  .BuyerRequests .content-title {
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .BuyerRequests .table {
    overflow-x: auto;
  }

  .BuyerRequests .requests-filters {
    padding: var(--smallfont);
  }

  .BuyerRequests .stat-item {
    min-width: 70px;
    padding: var(--extrasmallfont);
  }

  .BuyerRequests .stat-count {
    font-size: var(--heading5);
  }
}
