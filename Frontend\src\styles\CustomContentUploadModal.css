.custom-content-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.custom-content-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--light-gray);
}

.modal-header h2 {
  margin: 0;
  color: var(--secondary-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--gray);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--light-gray);
  color: var(--secondary-color);
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-body {
  padding: 1.5rem;
}

.request-info {
  background: var(--light-background);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid var(--primary-color);
}

.request-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--secondary-color);
  font-size: 1.1rem;
}

.request-info p {
  margin: 0.25rem 0;
  color: var(--gray);
  font-size: 0.9rem;
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 0.75rem;
  border: 2px solid var(--light-gray);
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.file-upload-area {
  margin-top: 0.5rem;
}

.file-upload-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  border: 2px dashed var(--light-gray);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--light-background);
}

.file-upload-label:hover {
  border-color: var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.05);
}

.file-upload-label.uploading {
  cursor: not-allowed;
  opacity: 0.7;
}

.upload-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 0;
  color: var(--gray);
  font-weight: 500;
}

.progress-bar {
  width: 200px;
  height: 6px;
  background: var(--light-gray);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--light-gray);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-secondary {
  background: var(--light-gray);
  color: var(--gray);
}

.btn-secondary:hover {
  background: var(--gray);
  color: white;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-primary:disabled,
.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Success Modal */
.success-modal {
  max-width: 400px;
  text-align: center;
}

.success-content {
  padding: 2rem;
}

.success-icon {
  font-size: 4rem;
  color: var(--success-color);
  margin-bottom: 1rem;
}

.success-content h2 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.success-content p {
  color: var(--gray);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-content-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .modal-header,
  .modal-body {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
  }
}

/* CSS Variables (add these to your main CSS file if not already present) */
:root {
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --primary-color-rgb: 0, 123, 255;
  --secondary-color: #333;
  --gray: #666;
  --light-gray: #e9ecef;
  --light-background: #f8f9fa;
  --success-color: #28a745;
  --error-color: #dc3545;
}
